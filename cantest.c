/*
 * Copyright (c) 2025 NXP Semiconductors
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 * cantest.c - CAN bus testing and benchmarking tool
 *
 * This program provides capabilities for testing CAN bus communication,
 * including randomized frame generation, performance benchmarking,
 * and file transfer over CAN.
 *
 * Data Flow Diagram:
 *
 * Transmit Mode (TX):
 * ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
 * │ Generate    │────>│ Write to    │────>│ Buffer      │
 * │ CAN Frames  │     │ CAN Socket  │     │ Frames      │
 * └─────────────┘     └─────────────┘     └─────────────┘
 *
 * Receive Mode (RX):
 * ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
 * │ Read from   │────>│ Store       │────>│ Display     │
 * │ CAN Socket  │     │ Frames      │     │ Statistics  │
 * └─────────────┘     └─────────────┘     └─────────────┘
 *
 * File Transfer:
 * ┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌────────────┐
 * │ Read File   │────>│ Transmit as │────>│ Receive and │────>│ Verify and │
 * │ Contents    │     │ CAN Frames  │     │ Reconstruct │     │ Save File  │
 * └─────────────┘     └─────────────┘     └─────────────┘     └────────────┘
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h>
#include <signal.h>
#include <stdbool.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <linux/can.h>
#include <linux/can/raw.h>
#include <getopt.h>
#include <stdint.h>
#include <errno.h>
#define A 0x67452301
#define B 0xefcdab89
#define C 0x98badcfe
#define D 0x10325476
#define F(x, y, z) ((x & y) | (~x & z))
#define G(x, y, z) ((x & z) | (y & ~z))
#define H(x, y, z) (x ^ y ^ z)
#define I(x, y, z) (y ^ (x | ~z))
static const uint32_t S[] = {
    7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22, 7, 12, 17, 22,
    5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20, 5, 9, 14, 20,
    4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23, 4, 11, 16, 23,
    6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21, 6, 10, 15, 21
};
static const uint32_t K[] = {
    0xd76aa478, 0xe8c7b756, 0x242070db, 0xc1bdceee,
    0xf57c0faf, 0x4787c62a, 0xa8304613, 0xfd469501,
    0x698098d8, 0x8b44f7af, 0xffff5bb1, 0x895cd7be,
    0x6b901122, 0xfd987193, 0xa679438e, 0x49b40821,
    0xf61e2562, 0xc040b340, 0x265e5a51, 0xe9b6c7aa,
    0xd62f105d, 0x02441453, 0xd8a1e681, 0xe7d3fbc8,
    0x21e1cde6, 0xc33707d6, 0xf4d50d87, 0x455a14ed,
    0xa9e3e905, 0xfcefa3f8, 0x676f02d9, 0x8d2a4c8a,
    0xfffa3942, 0x8771f681, 0x6d9d6122, 0xfde5380c,
    0xa4beea44, 0x4bdecfa9, 0xf6bb4b60, 0xbebfbc70,
    0x289b7ec6, 0xeaa127fa, 0xd4ef3085, 0x04881d05,
    0xd9d4d039, 0xe6db99e5, 0x1fa27cf8, 0xc4ac5665,
    0xf4292244, 0x432aff97, 0xab9423a7, 0xfc93a039,
    0x655b59c3, 0x8f0ccc92, 0xffeff47d, 0x85845dd1,
    0x6fa87e4f, 0xfe2ce6e0, 0xa3014314, 0x4e0811a1,
    0xf7537e82, 0xbd3af235, 0x2ad7d2bb, 0xeb86d391
};
typedef struct {
    uint64_t size;
    uint32_t buffer[4];
    uint8_t input[64];
    uint8_t digest[16];
} MD5Context;
void md5Init(MD5Context *ctx);
void md5Update(MD5Context *ctx, uint8_t *input, size_t input_len);
void md5Finalize(MD5Context *ctx);
void md5Step(uint32_t *buffer, uint32_t *input);
void md5String(uint8_t *input, size_t input_len, uint8_t *result);
void free_system_caches(void);
int can_frame_write_with_backoff(int socket_fd, struct can_frame *frame, int *interval_ns_ptr);
static inline uint32_t rotateLeft(uint32_t x, uint32_t n) {
    return (x << n) | (x >> (32 - n));
}
/* Initialize MD5 context with starting values */
void md5Init(MD5Context *ctx) {
    ctx->size = 0;
    ctx->buffer[0] = A;
    ctx->buffer[1] = B;
    ctx->buffer[2] = C;
    ctx->buffer[3] = D;
}
/* Update MD5 context with new data chunk */
void md5Update(MD5Context *ctx, uint8_t *input_buffer, size_t input_len) {
    uint32_t input[16];
    unsigned int offset = ctx->size % 64;
    ctx->size += (uint64_t)input_len;
    for (unsigned int i = 0; i < input_len; ++i) {
        ctx->input[offset++] = (uint8_t)*(input_buffer + i);
        if (offset % 64 == 0) {
            for (unsigned int j = 0; j < 16; ++j) {
                input[j] = (uint32_t)(ctx->input[(j * 4) + 3]) << 24 |
                           (uint32_t)(ctx->input[(j * 4) + 2]) << 16 |
                           (uint32_t)(ctx->input[(j * 4) + 1]) << 8 |
                           (uint32_t)(ctx->input[(j * 4)]);
            }
            md5Step(ctx->buffer, input);
            offset = 0;
        }
    }
}
void md5Finalize(MD5Context *ctx) {
    uint32_t input[16];
    unsigned int offset = ctx->size % 64;
    unsigned int padding_length = offset < 56 ? 56 - offset : (56 + 64) - offset;
    uint8_t padding[64] = {0};
    padding[0] = 0x80;
    md5Update(ctx, padding, padding_length);
    uint64_t size_in_bits = ctx->size * 8;
    uint8_t size_bytes[8];
    for (int i = 0; i < 8; i++) {
        size_bytes[i] = (size_in_bits >> (i * 8)) & 0xFF;
    }
    md5Update(ctx, size_bytes, 8);
    for (unsigned int i = 0; i < 4; ++i) {
        ctx->digest[(i * 4) + 0] = (uint8_t)((ctx->buffer[i] & 0x000000FF));
        ctx->digest[(i * 4) + 1] = (uint8_t)((ctx->buffer[i] & 0x0000FF00) >> 8);
        ctx->digest[(i * 4) + 2] = (uint8_t)((ctx->buffer[i] & 0x00FF0000) >> 16);
        ctx->digest[(i * 4) + 3] = (uint8_t)((ctx->buffer[i] & 0xFF000000) >> 24);
    }
}
void md5Step(uint32_t *buffer, uint32_t *input) {
    uint32_t AA = buffer[0];
    uint32_t BB = buffer[1];
    uint32_t CC = buffer[2];
    uint32_t DD = buffer[3];
    uint32_t E;
    unsigned int j;
    for (unsigned int i = 0; i < 64; ++i) {
        switch (i / 16) {
            case 0:
                E = F(BB, CC, DD);
                j = i;
                break;
            case 1:
                E = G(BB, CC, DD);
                j = ((i * 5) + 1) % 16;
                break;
            case 2:
                E = H(BB, CC, DD);
                j = ((i * 3) + 5) % 16;
                break;
            default:
                E = I(BB, CC, DD);
                j = (i * 7) % 16;
                break;
        }
        uint32_t temp = DD;
        DD = CC;
        CC = BB;
        BB = BB + rotateLeft(AA + E + K[i] + input[j], S[i]);
        AA = temp;
    }
    buffer[0] += AA;
    buffer[1] += BB;
    buffer[2] += CC;
    buffer[3] += DD;
}
void md5String(uint8_t *input, size_t input_len, uint8_t *result) {
    MD5Context ctx;
    md5Init(&ctx);
    md5Update(&ctx, input, input_len);
    md5Finalize(&ctx);
    memcpy(result, ctx.digest, 16);
}
void printMD5(uint8_t *md5_sum) {
    for (int i = 0; i < 16; i++) {
        printf("%02x", md5_sum[i]);
    }
    printf("\n");
}
/* Buffer structure for storing CAN frames */
struct {
    struct can_frame *frames;  /* Array of CAN frames */
    int count;                 /* Current number of frames in buffer */
    int capacity;              /* Total buffer capacity */
} frame_buffer;
/* Global variables for program control and statistics */
static volatile int keep_running = 1;     /* Flag for program control loop */
static int socket_fd = -1;                /* CAN socket file descriptor */
static int debug_mode = 0;                /* Debug output control */
static int default_buffer_size = 1000000; /* Default frame buffer size */
static int file_mode = 0;                 /* File transfer mode flag */
static char *file_path = NULL;            /* Path to file for transfer */
static struct timespec last_write_time;    /* Timestamp of last frame write */
static double real_delay_us = 0;          /* Actual delay between frames in μs */
static int min_dlc = 0;                   /* Minimum data length code */
static int max_dlc = 8;                   /* Maximum data length code */
static int fixed_dlc = -1;                /* Fixed data length code (if set) */
static long sum_real_delay_us = 0;        /* Sum of delays for statistics */
static int stat_count = 0;                /* Counter for statistics averaging */

/* 异常情况超时保护参数 */
static int global_timeout_seconds = 60;   /* 等待初始帧的超时时间（秒），用于检测发送端是否启动 */
static int frame_timeout_seconds = 5;     /* 单次select调用的超时时间（秒） */
static int max_loop_iterations = 10000000; /* 异常情况下的最大循环迭代次数保护（仅用于异常检测） */

/* 错误处理和自动退出参数 */
static int max_consecutive_errors = 10;   /* 最大连续错误次数，超过后自动退出 */
static int consecutive_error_count = 0;   /* 当前连续错误计数器 */
static int total_error_count = 0;         /* 总错误计数器 */
/* Function prototypes for high-level file transfer operations */
int transmit_file(const char *ifname, int interval_ns, const char *file_path, uint32_t fixed_can_id);
int receive_file(const char *ifname, const char *file_path);
/* Calculate MD5 checksum for a file
 *
 * @param file_path: Path to the file to calculate MD5 for
 * @param result: Buffer to store the resulting 16-byte MD5 hash
 */
void calculate_file_md5(const char *file_path, uint8_t *result) {
    FILE *file = fopen(file_path, "rb");
    if (!file) {
        perror("Failed to open file for MD5 calculation");
        memset(result, 0, 16);
        return;
    }
    MD5Context ctx;
    md5Init(&ctx);
    uint8_t buffer[4096];
    size_t bytes_read;
    while ((bytes_read = fread(buffer, 1, sizeof(buffer), file)) > 0) {
        md5Update(&ctx, buffer, bytes_read);
    }
    md5Finalize(&ctx);
    memcpy(result, ctx.digest, 16);
    fclose(file);
}
/* Initialize or reinitialize the CAN frame buffer with specified capacity
 *
 * @param capacity: Number of frames to allocate space for
 */
void init_frame_buffer(int capacity) {
    if (capacity <= 0) {
        capacity = default_buffer_size;
    }
    if (frame_buffer.frames != NULL) {
        free(frame_buffer.frames);
        frame_buffer.frames = NULL;
    }
    frame_buffer.frames = (struct can_frame *)malloc(capacity * sizeof(struct can_frame));
    if (frame_buffer.frames == NULL) {
        perror("Failed to allocate frame buffer");
        printf("Requested buffer size: %d frames (%zu bytes)\n",
               capacity, capacity * sizeof(struct can_frame));
        exit(EXIT_FAILURE);
    }
    frame_buffer.count = 0;
    frame_buffer.capacity = capacity;
}
/* Add a CAN frame to the buffer, expanding capacity if needed
 *
 * @param frame: Pointer to the CAN frame to add
 */
void add_frame_to_buffer(struct can_frame *frame) {
    if (frame_buffer.count >= frame_buffer.capacity) {
        int new_capacity = frame_buffer.capacity + (frame_buffer.capacity / 2);
        if (new_capacity <= frame_buffer.capacity) {
            new_capacity = frame_buffer.capacity + 1000;
        }
        struct can_frame *new_frames = (struct can_frame *)realloc(
            frame_buffer.frames, new_capacity * sizeof(struct can_frame));
        if (new_frames == NULL) {
            perror("Failed to resize frame buffer");
            printf("Buffer capacity reached: %d frames. Cannot allocate more memory.\n",
                   frame_buffer.capacity);
            printf("Stopping frame reception. Some frames may be lost.\n");
            keep_running = 0;
            return;
        }
        frame_buffer.frames = new_frames;
        frame_buffer.capacity = new_capacity;
    }
    frame_buffer.frames[frame_buffer.count++] = *frame;
}
/* Release memory used by the frame buffer */
void free_frame_buffer() {
    if (frame_buffer.frames != NULL) {
        free(frame_buffer.frames);
        frame_buffer.frames = NULL;
    }
    frame_buffer.count = 0;
    frame_buffer.capacity = 0;
}
/* Signal handler for graceful program termination
 *
 * @param sig: Signal number that was received
 */
void signal_handler(int sig) {
    printf("\nReceived signal %d, stopping...\n", sig);
    keep_running = 0;
}
/* Initialize CAN socket for the specified interface
 *
 * @param ifname: Name of the CAN interface (e.g., "can0")
 * @return: Socket file descriptor on success, -1 on failure
 */
int init_can_socket(const char *ifname) {
    int s;
    struct sockaddr_can addr;
    struct ifreq ifr;
    if ((s = socket(PF_CAN, SOCK_RAW, CAN_RAW)) < 0) {
        perror("Socket");
        return -1;
    }
    strcpy(ifr.ifr_name, ifname);
    if (ioctl(s, SIOCGIFINDEX, &ifr) < 0) {
        perror("ioctl");
        close(s);
        return -1;
    }
    memset(&addr, 0, sizeof(addr));
    addr.can_family = AF_CAN;
    addr.can_ifindex = ifr.ifr_ifindex;
    if (bind(s, (struct sockaddr *)&addr, sizeof(addr)) < 0) {
        perror("Bind");
        close(s);
        return -1;
    }
    return s;
}
/* Generate a random CAN frame
 *
 * @param frame: Pointer to the CAN frame structure to fill
 * @param fixed_can_id: If non-zero, use this as the CAN ID instead of random
 */
void generate_random_frame(struct can_frame *frame, uint32_t fixed_can_id) {
    if (fixed_can_id != 0) {
        frame->can_id = fixed_can_id & 0x7FF;
    } else {
        do {
            frame->can_id = rand() % 0x7FB;
        } while (frame->can_id >= 0x7FB && frame->can_id <= 0x7FF);
    }
    if (fixed_dlc >= 0 && fixed_dlc <= 8) {
        frame->can_dlc = fixed_dlc;
    } else {
        frame->can_dlc = min_dlc + (rand() % (max_dlc - min_dlc + 1));
    }
    for (int i = 0; i < frame->can_dlc; i++) {
        frame->data[i] = rand() % 256;
    }
}
/* Calculate MD5 hash for all CAN IDs in the frame buffer
 *
 * @param result: Buffer to store the resulting 16-byte MD5 hash
 */
void calculate_can_id_md5(uint8_t *result) {
    memset(result, 0, 16);
    if (frame_buffer.count == 0 || frame_buffer.frames == NULL) {
        printf("Warning: No frames to calculate MD5 for CAN IDs\n");
        return;
    }
    uint32_t *can_ids = malloc(frame_buffer.count * sizeof(uint32_t));
    if (!can_ids) {
        perror("Memory allocation failed in calculate_can_id_md5");
        printf("Warning: Unable to calculate MD5 for CAN IDs due to memory allocation failure\n");
        return;
    }
    for (int i = 0; i < frame_buffer.count; i++) {
        can_ids[i] = frame_buffer.frames[i].can_id;
    }
    md5String((uint8_t *)can_ids, frame_buffer.count * sizeof(uint32_t), result);
    free(can_ids);
}
/* Calculate MD5 hash for all CAN data bytes in the frame buffer
 *
 * @param result: Buffer to store the resulting 16-byte MD5 hash
 */
void calculate_can_data_md5(uint8_t *result) {
    memset(result, 0, 16);
    if (frame_buffer.count == 0 || frame_buffer.frames == NULL) {
        printf("Warning: No frames to calculate MD5 for CAN data\n");
        return;
    }
    size_t total_size = 0;
    for (int i = 0; i < frame_buffer.count; i++) {
        total_size += frame_buffer.frames[i].can_dlc;
    }
    if (total_size == 0) {
        printf("Warning: No data to calculate MD5 for\n");
        return;
    }
    uint8_t *data_buffer = malloc(total_size);
    if (!data_buffer) {
        perror("Memory allocation failed in calculate_can_data_md5");
        printf("Warning: Unable to calculate MD5 for CAN data due to memory allocation failure\n");
        return;
    }
    size_t offset = 0;
    for (int i = 0; i < frame_buffer.count; i++) {
        memcpy(data_buffer + offset, frame_buffer.frames[i].data, frame_buffer.frames[i].can_dlc);
        offset += frame_buffer.frames[i].can_dlc;
    }
    md5String(data_buffer, total_size, result);
    free(data_buffer);
}
/* Display a progress bar with performance statistics
 *
 * @param current: Current progress value
 * @param total: Maximum progress value
 * @param width: Width of the progress bar in characters
 * @param elapsed_seconds: Time elapsed in seconds
 * @param current_interval_ns: Current transmission interval in nanoseconds
 */
void display_progress_bar(int current, int total, int width, double elapsed_seconds, int current_interval_ns) {
    float progress = (float)current / total;
    if (progress > 1.0) progress = 1.0;
    int filled_width = (int)(progress * width);
    if (filled_width > width) filled_width = width;
    int percent = (int)(progress * 100);
    if (percent > 100) percent = 100;
    int current_fps = 0;
    if (elapsed_seconds > 0) {
        current_fps = (int)(current / elapsed_seconds);
    }
    int real_delay_us_int = (int)real_delay_us;
    int avg_real_delay_us = 0;
    if (stat_count > 0) {
        if (current == total) {
            avg_real_delay_us = (int)(sum_real_delay_us / stat_count);
            real_delay_us_int = avg_real_delay_us;
        }
    }
    printf("\r[%3d%%] [", percent);
    for (int i = 0; i < filled_width; i++) {
        printf("█");
    }
    for (int i = filled_width; i < width; i++) {
        printf(" ");
    }
    printf("] %d/%d FPS: %d RDelay: %d Time: %.1f",
           current, total, current_fps,
           real_delay_us_int, elapsed_seconds);
    printf("s");
    fflush(stdout);
}
/* Transmit CAN frames (simplified version without MD5 verification)
 *
 * This function generates and transmits a specified number of CAN frames.
 * No special frames or MD5 verification is performed.
 *
 * @param ifname: Name of the CAN interface
 * @param interval_ns: Time interval between frames in nanoseconds
 * @param max_frames: Maximum number of frames to send (must be > 0)
 * @param fixed_can_id: If non-zero, use this CAN ID for all frames
 */
void transmit_frames(const char *ifname, int interval_ns, int max_frames, uint32_t fixed_can_id) {
    struct timespec ts;
    struct timespec start_time, end_time;
    double elapsed_seconds;
    int progress_bar_width = 25;
    int update_interval = 100;
    int dynamic_interval_ns = interval_ns;

    if (max_frames <= 0) {
        printf("Error: max_frames must be greater than 0\n");
        return;
    }

    ts.tv_sec = interval_ns / 1000000000;
    ts.tv_nsec = interval_ns % 1000000000;

    init_frame_buffer(max_frames);
    printf("TX Start send %d frames\n", max_frames);
    clock_gettime(CLOCK_MONOTONIC, &start_time);

    update_interval = max_frames / 100;
    if (update_interval < 1) update_interval = 1;

    while (keep_running && frame_buffer.count < max_frames) {
        struct can_frame frame;
        generate_random_frame(&frame, fixed_can_id);

        int write_result = can_frame_write_with_backoff(socket_fd, &frame, &dynamic_interval_ns);
        if (write_result < 0) {
            if (write_result == -2) {
                /* 致命错误，退出循环 */
                printf("Fatal error occurred, stopping transmission.\n");
                break;
            }
            /* 普通错误，继续尝试 */
            if (consecutive_error_count <= 3) {
                printf("Warning: Failed to send frame %d (consecutive errors: %d)\n",
                       frame_buffer.count, consecutive_error_count);
            }
            continue;
        }

        add_frame_to_buffer(&frame);

        if (debug_mode) {
            printf("%s %03X [%d] ", ifname, frame.can_id & CAN_SFF_MASK, frame.can_dlc);
            for (int i = 0; i < frame.can_dlc; i++) {
                printf("%02X", frame.data[i]);
                if (i < frame.can_dlc - 1) {
                    printf(" ");
                }
            }
            printf("\n");
        }

        if (frame_buffer.count % update_interval == 0 || frame_buffer.count == max_frames) {
            struct timespec current_time;
            clock_gettime(CLOCK_MONOTONIC, &current_time);
            double current_elapsed = (current_time.tv_sec - start_time.tv_sec) +
                                    (current_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
            display_progress_bar(frame_buffer.count, max_frames, progress_bar_width, current_elapsed, dynamic_interval_ns);
        }

        if (dynamic_interval_ns > 0) {
            ts.tv_sec = dynamic_interval_ns / 1000000000;
            ts.tv_nsec = dynamic_interval_ns % 1000000000;
            nanosleep(&ts, NULL);
        }
    }
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    elapsed_seconds = (end_time.tv_sec - start_time.tv_sec) +
                      (end_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
    double fps = frame_buffer.count / elapsed_seconds;
    int fps_int = (int)fps;

    display_progress_bar(frame_buffer.count, max_frames, progress_bar_width, elapsed_seconds, dynamic_interval_ns);
    printf("\n");
    printf("TX: Sent %d/%d frames, FPS: %d, Time: %.1fs\n",
           frame_buffer.count, max_frames, fps_int, elapsed_seconds);

    sum_real_delay_us = 0;
    stat_count = 0;
}
/* Simplified receive function - no special frames needed
 * Just receives the specified number of frames directly
 */
/* Receive CAN frames (simplified version without MD5 verification)
 *
 * This function receives a specified number of CAN frames and stores them in a buffer.
 * No special frames or MD5 verification is performed.
 *
 * @param ifname: Name of the CAN interface
 * @param max_frames: Maximum number of frames to receive (must be > 0)
 */
void receive_frames(const char *ifname, int max_frames) {
    struct can_frame frame;
    fd_set readfds;
    int nbytes;
    struct timespec start_time, end_time, last_read_time = {0};
    double elapsed_seconds;
    int progress_bar_width = 25;
    int update_interval = 100;
    double frame_delay_us = 0;

    if (max_frames <= 0) {
        printf("Error: max_frames must be greater than 0\n");
        return;
    }

    init_frame_buffer(max_frames);
    update_interval = max_frames / 100;
    if (update_interval < 1) update_interval = 1;

    printf("RX Waiting to receive %d frames...\n", max_frames);
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    last_read_time = start_time;

    while (keep_running && frame_buffer.count < max_frames) {
        FD_ZERO(&readfds);
        FD_SET(socket_fd, &readfds);

        // 永远等待，直到收到帧或被中断
        int ret = select(socket_fd + 1, &readfds, NULL, NULL, NULL);
        if (ret < 0) {
            if (errno == EINTR) {
                if (!keep_running) break;
                continue;
            }
            perror("select");
            break;
        }

        if (!FD_ISSET(socket_fd, &readfds)) {
            continue;
        }

        nbytes = read(socket_fd, &frame, sizeof(struct can_frame));
        if (nbytes < 0) {
            perror("Read");
            break;
        }

        struct timespec current_read_time;
        clock_gettime(CLOCK_MONOTONIC, &current_read_time);
        if (last_read_time.tv_sec != 0) {
            double diff_sec = (current_read_time.tv_sec - last_read_time.tv_sec) +
                             (current_read_time.tv_nsec - last_read_time.tv_nsec) / 1000000000.0;
            frame_delay_us = diff_sec * 1000000.0;
            sum_real_delay_us += (long)frame_delay_us;
            stat_count++;
            real_delay_us = frame_delay_us;
        }
        last_read_time = current_read_time;

        add_frame_to_buffer(&frame);

        if (debug_mode) {
            printf("%s %03X [%d] ", ifname, frame.can_id & CAN_SFF_MASK, frame.can_dlc);
            for (int i = 0; i < frame.can_dlc; i++) {
                printf("%02X", frame.data[i]);
                if (i < frame.can_dlc - 1) {
                    printf(" ");
                }
            }
            printf("\n");
        }

        if (frame_buffer.count % update_interval == 0 || frame_buffer.count == max_frames) {
            struct timespec current_time;
            clock_gettime(CLOCK_MONOTONIC, &current_time);
            double current_elapsed = (current_time.tv_sec - start_time.tv_sec) +
                                    (current_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
            display_progress_bar(frame_buffer.count, max_frames, progress_bar_width, current_elapsed, 0);
        }
    }
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    elapsed_seconds = (end_time.tv_sec - start_time.tv_sec) +
                      (end_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
    int actual_frame_count = frame_buffer.count;
    double fps = frame_buffer.count / elapsed_seconds;
    int fps_int = (int)fps;

    display_progress_bar(frame_buffer.count, max_frames, progress_bar_width, elapsed_seconds, 0);
    printf("\n");
    printf("RX: Received %d/%d frames, FPS: %d, Time: %.1fs\n",
           actual_frame_count, max_frames, fps_int, elapsed_seconds);
    if (actual_frame_count > 0) {
        printf("\nSample frame data:\n");
        printf("First frame: ID=0x%03X DLC=%d Data=",
               frame_buffer.frames[0].can_id & 0x7FF,
               frame_buffer.frames[0].can_dlc);
        for (int j = 0; j < frame_buffer.frames[0].can_dlc; j++) {
            printf("%02X ", frame_buffer.frames[0].data[j]);
        }
        printf("\n");
        if (actual_frame_count > 1) {
            int last_idx = actual_frame_count - 1;
            printf("Last frame: ID=0x%03X DLC=%d Data=",
                   frame_buffer.frames[last_idx].can_id & 0x7FF,
                   frame_buffer.frames[last_idx].can_dlc);
            for (int j = 0; j < frame_buffer.frames[last_idx].can_dlc; j++) {
                printf("%02X ", frame_buffer.frames[last_idx].data[j]);
            }
            printf("\n");
        }

        if (actual_frame_count == max_frames) {
            printf("\033[32mSUCCESS:\033[0m Received all expected frames (100%%)\n");
        } else {
            printf("\033[33mWARNING:\033[0m Received %d frames, expected %d frames\n",
                   actual_frame_count, max_frames);
        }
    } else {
        printf("\nNo frames received.\n");
    }

    sum_real_delay_us = 0;
    stat_count = 0;
}
/* Transmit a file over CAN bus
 *
 * This function breaks a file into chunks and transmits it as a series of CAN frames.
 * It sends metadata frames with file size and MD5 checksum for verification.
 *
 * @param ifname: Name of the CAN interface
 * @param interval_ns: Interval between frames in nanoseconds
 * @param file_path: Path to the file to transmit
 * @param fixed_can_id: If non-zero, use this CAN ID for all data frames
 * @return: 0 on success, -1 on failure
 */
int transmit_file(const char *ifname, int interval_ns, const char *file_path, uint32_t fixed_can_id) {
    FILE *file = fopen(file_path, "rb");
    if (!file) {
        perror("Failed to open file for transmission");
        return -1;
    }
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);
    if (file_size <= 0) {
        printf("Error: File is empty or invalid\n");
        fclose(file);
        return -1;
    }
    int frame_count = (file_size + 7) / 8;
    int dynamic_interval_ns = interval_ns;
    printf("TX File: %s, Size: %ld bytes, Frames: %d\n", file_path, file_size, frame_count);
    if (frame_count <= 0) {
        printf("Error: Invalid frame count calculated (%d). File size: %ld bytes\n",
               frame_count, file_size);
        fclose(file);
        return -1;
    }
    struct timespec ts;
    ts.tv_sec = interval_ns / 1000000000;
    ts.tv_nsec = interval_ns % 1000000000;
    int progress_bar_width = 25;
    int update_interval = frame_count / 100;
    if (update_interval < 1) update_interval = 1;
    uint8_t file_md5[16];
    calculate_file_md5(file_path, file_md5);
    printf("File MD5: ");
    for (int i = 0; i < 16; i++) {
        printf("%02x", file_md5[i]);
    }
    printf("\n");
    struct can_frame info_frame;
    info_frame.can_id = 0x7FA;
    info_frame.can_dlc = 8;
    info_frame.data[0] = (file_size >> 0) & 0xFF;
    info_frame.data[1] = (file_size >> 8) & 0xFF;
    info_frame.data[2] = (file_size >> 16) & 0xFF;
    info_frame.data[3] = (file_size >> 24) & 0xFF;
    info_frame.data[4] = (frame_count >> 0) & 0xFF;
    info_frame.data[5] = (frame_count >> 8) & 0xFF;
    info_frame.data[6] = (frame_count >> 16) & 0xFF;
    info_frame.data[7] = (frame_count >> 24) & 0xFF;
    if (debug_mode) {
        printf("Sending info frame with file size: %ld bytes, frame count: %d\n", file_size, frame_count);
    }
    if (can_frame_write_with_backoff(socket_fd, &info_frame, &dynamic_interval_ns) < 0) {
        printf("Warning: Failed to send info frame, but continuing...\n");
    }
    struct timespec long_delay;
    long_delay.tv_sec = 1;
    long_delay.tv_nsec = 0;
    printf("TX: Waiting for RX to process info frame...\n");
    nanosleep(&long_delay, NULL);
    struct timespec start_time, end_time;
    clock_gettime(CLOCK_MONOTONIC, &start_time);
    uint8_t buffer[8];
    int frames_sent = 0;
    stat_count = 1;

    while (frames_sent < frame_count && keep_running) {
        /* 正常的文件传输循环，由文件大小控制，不限制时间和迭代次数 */
        struct can_frame frame;
        if (fixed_can_id != 0) {
            frame.can_id = fixed_can_id & 0x7FF;
        } else {
            do {
                frame.can_id = rand() % 0x7FB;
            } while (frame.can_id >= 0x7FB && frame.can_id <= 0x7FF);
        }
        size_t bytes_read = fread(buffer, 1, 8, file);
        if (bytes_read == 0) {
            printf("\nEnd of file reached after sending %d frames\n", frames_sent);
            break;
        }
        frame.can_dlc = bytes_read;
        memcpy(frame.data, buffer, bytes_read);
        int write_result = can_frame_write_with_backoff(socket_fd, &frame, &dynamic_interval_ns);
        if (write_result < 0) {
            if (write_result == -2) {
                /* 致命错误，退出循环 */
                printf("Fatal error occurred, stopping file transmission.\n");
                break;
            }
            /* 普通错误，继续尝试 */
            if (consecutive_error_count <= 3) {
                printf("Warning: Failed to send data frame %d (consecutive errors: %d)\n",
                       frames_sent, consecutive_error_count);
            }
            continue;
        }
        stat_count++;
        frames_sent++;
        if (frames_sent % update_interval == 0 || frames_sent == frame_count) {
            struct timespec current_time;
            clock_gettime(CLOCK_MONOTONIC, &current_time);
            double current_elapsed = (current_time.tv_sec - start_time.tv_sec) +
                                    (current_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
            display_progress_bar(frames_sent, frame_count, progress_bar_width, current_elapsed, dynamic_interval_ns);
        }
        if (dynamic_interval_ns > 0) {
            ts.tv_sec = dynamic_interval_ns / 1000000000;
            ts.tv_nsec = dynamic_interval_ns % 1000000000;
            nanosleep(&ts, NULL);
        }
        if (frames_sent >= frame_count) {
            printf("\nAll %d frames sent successfully\n", frames_sent);
            break;
        }
    }
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    double elapsed_seconds = (end_time.tv_sec - start_time.tv_sec) +
                           (end_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
    double fps = frames_sent / elapsed_seconds;
    int fps_int = (int)fps;
    int avg_real_delay_us = 0;
    if (stat_count > 0) {
        avg_real_delay_us = (int)(sum_real_delay_us / stat_count);
    }
    display_progress_bar(frames_sent, frame_count, progress_bar_width, elapsed_seconds, dynamic_interval_ns);
    printf("\n");
    printf("TX: %d/%d FPS: %d\n", frames_sent, frame_count, fps_int);
    struct can_frame md5_frame1, md5_frame2;
    md5_frame1.can_id = 0x7FF;
    md5_frame1.can_dlc = 8;
    memcpy(md5_frame1.data, file_md5, 8);
    md5_frame2.can_id = 0x7FE;
    md5_frame2.can_dlc = 8;
    memcpy(md5_frame2.data, file_md5 + 8, 8);
    struct can_frame count_frame;
    count_frame.can_id = 0x7FB;
    count_frame.can_dlc = 8;
    count_frame.data[0] = (frames_sent >> 0) & 0xFF;
    count_frame.data[1] = (frames_sent >> 8) & 0xFF;
    count_frame.data[2] = (frames_sent >> 16) & 0xFF;
    count_frame.data[3] = (frames_sent >> 24) & 0xFF;
    count_frame.data[4] = (fps_int >> 0) & 0xFF;
    count_frame.data[5] = (fps_int >> 8) & 0xFF;
    count_frame.data[6] = (fps_int >> 16) & 0xFF;
    count_frame.data[7] = (fps_int >> 24) & 0xFF;
    if (can_frame_write_with_backoff(socket_fd, &md5_frame1, &dynamic_interval_ns) < 0) {
        printf("Warning: Failed to send MD5 frame 1\n");
    }
    nanosleep(&ts, NULL);
    if (can_frame_write_with_backoff(socket_fd, &md5_frame2, &dynamic_interval_ns) < 0) {
        printf("Warning: Failed to send MD5 frame 2\n");
    }
    nanosleep(&ts, NULL);
    if (can_frame_write_with_backoff(socket_fd, &count_frame, &dynamic_interval_ns) < 0) {
        printf("Warning: Failed to send count frame\n");
    }
    fclose(file);
    sum_real_delay_us = 0;
    stat_count = 0;
    return 0;
}
/* Free system caches to improve benchmark consistency
 *
 * This function attempts to free system memory caches to get more consistent
 * performance measurements. It drops kernel caches if possible and allocates
 * and frees memory to encourage garbage collection.
 */
void free_system_caches() {
    FILE *fp = fopen("/proc/sys/vm/drop_caches", "w");
    if (fp) {
        fprintf(fp, "3");
        fclose(fp);
    }
    for (int i = 0; i < 5; i++) {
        void *ptr = malloc(1024 * 1024);
        if (ptr) {
            memset(ptr, 0, 1024 * 1024);
            free(ptr);
        }
    }
    struct timespec ts;
    ts.tv_sec = 0;
    ts.tv_nsec = 100000000;
    nanosleep(&ts, NULL);
}
/* Receive a file transmitted over CAN bus
 *
 * This function receives a file sent as a series of CAN frames, validates its MD5 checksum,
 * and saves it to the specified path if the validation is successful.
 *
 * @param ifname: Name of the CAN interface
 * @param file_path: Path where the received file should be saved
 * @return: 0 on success, -1 on failure
 */
int receive_file(const char *ifname, const char *file_path) {
    struct can_frame frame;
    fd_set readfds;
    int ret;
    long file_size = 0;
    int expected_frame_count = 0;
    int frames_received = 0;
    uint8_t *file_buffer = NULL;
    long bytes_written = 0;
    int progress_bar_width = 25;
    int update_interval = 100;
    bool init_file_info = false;
    struct timespec start_time, end_time;
    struct timespec last_read_time = {0};
    double frame_delay_us = 0;
    uint8_t tx_md5[16] = {0};
    uint8_t rx_md5[16] = {0};
    int tx_frame_count = 0;
    int tx_fps = 0;
    int special_frames_received = 0;
    printf("RX Waiting for file info frame...\n");

    time_t info_start_time = time(NULL);
    struct timeval info_timeout;

    while (keep_running) {
        /* 异常保护：如果等待时间过长，可能发送端没有启动文件传输 */
        if (time(NULL) - info_start_time > global_timeout_seconds) {
            printf("Error: No file info frame received after %d seconds. Please check if file transmitter is running.\n", global_timeout_seconds);
            return -1;
        }

        FD_ZERO(&readfds);
        FD_SET(socket_fd, &readfds);

        /* 设置select超时 */
        info_timeout.tv_sec = frame_timeout_seconds;
        info_timeout.tv_usec = 0;

        ret = select(socket_fd + 1, &readfds, NULL, NULL, &info_timeout);
        if (ret < 0) {
            if (errno == EINTR) {
                if (!keep_running) return -1;
                continue;
            }
            perror("select");
            return -1;
        }
        if (ret == 0) {
            printf("Warning: No file info frame received within %d seconds, continuing to wait...\n", frame_timeout_seconds);
            continue;
        }
        if (!FD_ISSET(socket_fd, &readfds)) {
            continue;
        }
        ssize_t nbytes = read(socket_fd, &frame, sizeof(struct can_frame));
        if (nbytes < 0) {
            perror("Read");
            return -1;
        }
        if (frame.can_id == 0x7FA) {
            file_size = ((uint32_t)frame.data[0]) |
                       ((uint32_t)frame.data[1] << 8) |
                       ((uint32_t)frame.data[2] << 16) |
                       ((uint32_t)frame.data[3] << 24);
            expected_frame_count = ((uint32_t)frame.data[4]) |
                                  ((uint32_t)frame.data[5] << 8) |
                                  ((uint32_t)frame.data[6] << 16) |
                                  ((uint32_t)frame.data[7] << 24);
            if (file_size <= 0 || expected_frame_count <= 0 ) {
                printf("Error: Invalid file info frame (size: %ld, frames: %d)\n", file_size, expected_frame_count);
                return -1;
            }
            file_buffer = (uint8_t *)malloc(file_size);
            if (!file_buffer) {
                perror("Memory allocation failed");
                return -1;
            }
            init_file_info = true;
            printf("RX File info received: size=%ld bytes, frames=%d\n", file_size, expected_frame_count);
            printf("RX Start receiving file data...\n");
            clock_gettime(CLOCK_MONOTONIC, &start_time);
            stat_count = 0;
            sum_real_delay_us = 0;
            update_interval = expected_frame_count / 100;
            if (update_interval < 1) update_interval = 1;
            break;
        }
    }
    if (!file_buffer) {
        printf("Error: Did not receive file information frame\n");
        return -1;
    }
    last_read_time = start_time;
    struct timeval timeout;
    timeout.tv_sec = 3;
    timeout.tv_usec = 0;
    printf("RX Start receiving file data...\n");

    while (keep_running) {
        /* 正常的文件接收循环，由文件大小和特殊帧控制退出 */
        FD_ZERO(&readfds);
        FD_SET(socket_fd, &readfds);
        ret = select(socket_fd + 1, &readfds, NULL, NULL, &timeout);
        if (ret == 0) {
            if (frames_received > 0) {
                if (special_frames_received >= 3) {
                    printf("\nTimeout: No frames received for 3 seconds, but all special frames received\n");
                    break;
                } else {
                    static int timeout_count = 0;
                    timeout_count++;
                    if (timeout_count <= 2) {
                        printf("\nTimeout: No frames received for 3 seconds, waiting for special frames...\n");
                        timeout.tv_sec = 3;
                        timeout.tv_usec = 0;
                        continue;
                    } else {
                        printf("\nGiving up after %d timeouts. Not all special frames received.\n", timeout_count);
                        break;
                    }
                }
            } else {
                printf("\nTimeout: No frames received for 3 seconds\n");
                break;
            }
        }
        if (ret < 0) {
            if (errno == EINTR) {
                if (!keep_running) break;
                continue;
            }
            perror("select");
            break;
        }
        if (!FD_ISSET(socket_fd, &readfds)) {
            continue;
        }
        ssize_t nbytes = read(socket_fd, &frame, sizeof(struct can_frame));
        if (nbytes < 0) {
            perror("Read");
            break;
        }
        struct timespec current_read_time;
        clock_gettime(CLOCK_MONOTONIC, &current_read_time);
        if (last_read_time.tv_sec != 0) {
            double diff_sec = (current_read_time.tv_sec - last_read_time.tv_sec) +
                             (current_read_time.tv_nsec - last_read_time.tv_nsec) / 1000000000.0;
            frame_delay_us = diff_sec * 1000000.0;
            sum_real_delay_us += (long)frame_delay_us;
            stat_count++;
            real_delay_us = frame_delay_us;
        }
        last_read_time = current_read_time;
        if (frame.can_id == 0x7FF || frame.can_id == 0x7FE || frame.can_id == 0x7FB) {
            if (frame.can_id == 0x7FF) {
                memcpy(tx_md5, frame.data, 8);
                special_frames_received++;
                if (debug_mode) {
                    printf("Received TX MD5 frame (first 8 bytes)\n");
                }
            } else if (frame.can_id == 0x7FE) {
                memcpy(tx_md5 + 8, frame.data, 8);
                special_frames_received++;
                if (debug_mode) {
                    printf("Received TX MD5 frame (second 8 bytes)\n");
                }
            } else if (frame.can_id == 0x7FB) {
                tx_frame_count = ((uint32_t)frame.data[0]) |
                                ((uint32_t)frame.data[1] << 8) |
                                ((uint32_t)frame.data[2] << 16) |
                                ((uint32_t)frame.data[3] << 24);
                tx_fps = ((uint32_t)frame.data[4]) |
                        ((uint32_t)frame.data[5] << 8) |
                        ((uint32_t)frame.data[6] << 16) |
                        ((uint32_t)frame.data[7] << 24);
                special_frames_received++;
                if (debug_mode) {
                    printf("Received TX frame count frame: count=%d, fps=%d\n", tx_frame_count, tx_fps);
                }
            }
            if (special_frames_received >= 3 && frames_received >= expected_frame_count) {
                printf("\nAll special frames received\n");
                break;
            }
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;
            continue;
        }
        if (file_buffer && frames_received < expected_frame_count) {
            long offset = frames_received * 8;
            size_t bytes_to_write = frame.can_dlc;
            if (offset + bytes_to_write > file_size) {
                bytes_to_write = file_size - offset;
            }
            if (bytes_to_write > 0) {
                memcpy(file_buffer + offset, frame.data, bytes_to_write);
                bytes_written += bytes_to_write;
            }
            frames_received++;
            if (frames_received % update_interval == 0 || frames_received == expected_frame_count) {
                struct timespec current_time;
                clock_gettime(CLOCK_MONOTONIC, &current_time);
                double current_elapsed = (current_time.tv_sec - start_time.tv_sec) +
                                        (current_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
                display_progress_bar(frames_received, expected_frame_count, progress_bar_width, current_elapsed, 0);
            }
            timeout.tv_sec = 3;
            timeout.tv_usec = 0;
        }
        if (frames_received >= expected_frame_count && special_frames_received < 3) {
            if (frames_received == expected_frame_count) {
                printf("\nRX: Waiting for special frames...\n");
            }
        }
    }
    clock_gettime(CLOCK_MONOTONIC, &end_time);
    double elapsed_seconds = (end_time.tv_sec - start_time.tv_sec) +
                           (end_time.tv_nsec - start_time.tv_nsec) / 1000000000.0;
    double fps = frames_received / elapsed_seconds;
    int fps_int = (int)fps;
    int avg_real_delay_us = 0;
    if (stat_count > 0) {
        avg_real_delay_us = (int)(sum_real_delay_us / stat_count);
    }
    display_progress_bar(frames_received, expected_frame_count, progress_bar_width, elapsed_seconds, 0);
    printf("\n");
    printf("RX: %d/%d FPS: %d\n", frames_received, expected_frame_count, fps_int);
    MD5Context ctx;
    md5Init(&ctx);
    md5Update(&ctx, file_buffer, file_size);
    md5Finalize(&ctx);
    memcpy(rx_md5, ctx.digest, 16);
    printf("TX MD5: ");
    for (int i = 0; i < 16; i++) {
        printf("%02x", tx_md5[i]);
    }
    printf("\n");
    printf("RX MD5: ");
    for (int i = 0; i < 16; i++) {
        printf("%02x", rx_md5[i]);
    }
    printf("\n");
    bool md5_match = (memcmp(tx_md5, rx_md5, 16) == 0);
    if (md5_match) {
        printf("MD5 \033[32mMATCH\033[0m\n");
    } else {
        printf("MD5 \033[31mNOT MATCH\033[0m\n");
    }
    if (tx_frame_count != frames_received) {
        printf("\033[31mERROR:\033[0m Frame count mismatch: TX reported %d frames, RX received %d frames\n",
               tx_frame_count, frames_received);
    } else {
        printf("\033[32mPERFECT:\033[0m Received all frames (100%%)\n");
    }
    if (md5_match) {
        FILE *file = fopen(file_path, "wb");
        if (!file) {
            perror("Failed to open output file");
            free(file_buffer);
            return -1;
        }
        size_t written = fwrite(file_buffer, 1, file_size, file);
        if (written != file_size) {
            printf("Error: Only wrote %zu of %ld bytes to file\n", written, file_size);
            fclose(file);
            free(file_buffer);
            return -1;
        }
        fclose(file);
        printf("File saved to: %s\n", file_path);
    } else {
        printf("File not saved due to MD5 mismatch\n");
    }
    free(file_buffer);
    sum_real_delay_us = 0;
    stat_count = 0;
    return md5_match ? 0 : -1;
}
/* Print program usage information to stdout
 *
 * @param program_name: Name of the program executable
 */
void print_usage(const char *program_name) {
    printf("Usage: %s [options]\n", program_name);
    printf("Options:\n");
    printf(" -r Receive mode (default is transmit mode if not specified)\n");
    printf(" -t <interval> Set transmit interval in nanoseconds (default: 60000)\n");
    printf(" -n <count> Number of frames to send/receive (REQUIRED)\n");
    printf(" -i [interface] CAN interface name (default: can0)\n");
    printf(" -I [canid] Fixed CAN ID for transmission (default: random)\n");
    printf(" -d [0|1] Debug mode: 0=off, 1=on (default: 0)\n");
    printf(" -f [file] File transfer mode: TX reads from file, RX saves to file\n");
    printf(" -L [min-max] Set data length range for random frames (default: 0-8)\n");
    printf(" -l [length] Set fixed data length for all frames (0-8, overrides -L)\n");
    printf(" -T [seconds] Set timeout for waiting initial frames (default: 60)\n");
    printf(" -F [seconds] Set select timeout for each call (default: 5)\n");
    printf(" -M [count] Set max iterations for abnormal loop detection (default: 10000000)\n");
    printf(" -E [count] Set max consecutive errors before exit (default: 10)\n");
    printf(" -h Show this help message\n");
    printf("\nExamples:\n");
    printf(" %s -n 1000                    # Send 1000 frames\n", program_name);
    printf(" %s -r -n 1000                # Receive 1000 frames\n", program_name);
    printf(" %s -n 500 -I 0x123           # Send 500 frames with fixed CAN ID 0x123\n", program_name);
    printf(" %s -f input.txt              # Send file as CAN frames\n", program_name);
    printf(" %s -r -f output.txt          # Receive file from CAN frames\n", program_name);
}
/* Constants for the CAN frame write retry mechanism */
#define MAX_WRITE_RETRIES 10    /* Maximum number of write attempts */
#define INITIAL_BACKOFF_US 1000 /* Initial backoff time in microseconds */
#define MAX_BACKOFF_US 1000000  /* Maximum backoff time in microseconds */

/* Write a CAN frame with exponential backoff on buffer full
 *
 * This function attempts to write a CAN frame to the socket, with retry capability
 * using exponential backoff when the socket buffer is full. It also tracks timing
 * statistics for performance monitoring.
 *
 * @param socket_fd: Socket file descriptor
 * @param frame: Pointer to the CAN frame to send
 * @param interval_ns_ptr: Pointer to the interval value, may be updated
 * @return: 0 on success, -1 on failure
 */
int can_frame_write_with_backoff(int socket_fd, struct can_frame *frame, int *interval_ns_ptr) {
    int retries = 0;
    int backoff_time_us = INITIAL_BACKOFF_US;
    int ret;
    struct timespec current_write_time;
    clock_gettime(CLOCK_MONOTONIC, &current_write_time);
    if (last_write_time.tv_sec != 0) {
        double diff_sec = (current_write_time.tv_sec - last_write_time.tv_sec) +
                         (current_write_time.tv_nsec - last_write_time.tv_nsec) / 1000000000.0;
        real_delay_us = diff_sec * 1000000.0;
        sum_real_delay_us += (long)real_delay_us;
        stat_count++;
    }
    while (retries < MAX_WRITE_RETRIES) {
        ret = write(socket_fd, frame, sizeof(struct can_frame));
        if (ret == sizeof(struct can_frame)) {
            last_write_time = current_write_time;
            /* 成功发送，重置连续错误计数器 */
            consecutive_error_count = 0;
            return 0;
        }

        /* 检查错误类型 */
        if (errno != ENOBUFS) {
            total_error_count++;
            consecutive_error_count++;

            /* 致命错误，立即退出 */
            if (errno == ENODEV || errno == ENXIO || errno == ENOENT) {
                printf("Fatal error: CAN device not available (errno=%d: %s)\n", errno, strerror(errno));
                printf("Please check if CAN interface exists and is up.\n");
                return -2; /* 返回-2表示致命错误 */
            }

            /* 其他非缓冲区错误 */
            if (consecutive_error_count <= 3) {
                printf("Write error (not buffer related, attempt %d): %s\n", consecutive_error_count, strerror(errno));
            } else if (consecutive_error_count == 4) {
                printf("Suppressing further error messages (total errors: %d)...\n", total_error_count);
            }

            /* 检查是否达到最大连续错误次数 */
            if (consecutive_error_count >= max_consecutive_errors) {
                printf("Too many consecutive errors (%d), giving up.\n", consecutive_error_count);
                return -2; /* 返回-2表示需要退出 */
            }

            return -1;
        }

        /* ENOBUFS错误，继续重试 */
        usleep(backoff_time_us);
        backoff_time_us *= 2;
        if (backoff_time_us > MAX_BACKOFF_US)
            backoff_time_us = MAX_BACKOFF_US;
        retries++;
        if (debug_mode) {
            printf("缓冲区已满，重试发送 (尝试 %d/%d)\n", retries, MAX_WRITE_RETRIES);
        }
    }

    /* 重试次数用完 */
    consecutive_error_count++;
    total_error_count++;
    if (debug_mode) {
        printf("发送失败: 超过最大重试次数 %d (连续错误: %d)\n", MAX_WRITE_RETRIES, consecutive_error_count);
    }
    return -1;
}
/* Main program entry point
 *
 * Parses command line arguments and runs the appropriate CAN test mode.
 * Supports both transmit and receive modes, as well as file transfer.
 *
 * @param argc: Command line argument count
 * @param argv: Command line argument array
 * @return: EXIT_SUCCESS or EXIT_FAILURE
 */
int main(int argc, char **argv) {
    frame_buffer.frames = NULL;
    /*free_system_caches();*/
    int receive_mode = 0;
    int interval_ns = 60000;
    int max_frames = 0;
    char ifname[IFNAMSIZ] = "can0";
    uint32_t fixed_can_id = 0;
    char *length_range = NULL;

    /* Define command line options */
    static struct option long_options[] = {
        {"receive", no_argument, 0, 'r'},
        {"interval", required_argument, 0, 't'},
        {"count", required_argument, 0, 'n'},
        {"interface", required_argument, 0, 'i'},
        {"canid", required_argument, 0, 'I'},
        {"debug", required_argument, 0, 'd'},
        {"file", required_argument, 0, 'f'},
        {"length-range", required_argument, 0, 'L'},
        {"fixed-length", required_argument, 0, 'l'},
        {"global-timeout", required_argument, 0, 'T'},
        {"frame-timeout", required_argument, 0, 'F'},
        {"max-iterations", required_argument, 0, 'M'},
        {"max-errors", required_argument, 0, 'E'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    int opt;
    int option_index = 0;
    while ((opt = getopt_long(argc, argv, "rt:n:i:I:d:f:L:l:T:F:M:E:h", long_options, &option_index)) != -1) {
        switch (opt) {
            case 'r':
                receive_mode = 1;
                break;
            case 't':
                interval_ns = atoi(optarg);
                break;
            case 'n':
                max_frames = atoi(optarg);
                break;
            case 'i':
                strncpy(ifname, optarg, IFNAMSIZ - 1);
                break;
            case 'I':
                if (strncmp(optarg, "0x", 2) == 0) {
                    fixed_can_id = (uint32_t)strtol(optarg, NULL, 16);
                } else {
                    fixed_can_id = (uint32_t)strtol(optarg, NULL, 10);
                }
                if (fixed_can_id >= 0x7FB && fixed_can_id <= 0x7FF) {
                    printf("Error: CAN ID 0x%03X is reserved for special frames. Please use a different ID.\n", fixed_can_id);
                    return EXIT_FAILURE;
                }
                fixed_can_id &= 0x7FF;
                break;
            case 'd':
                debug_mode = atoi(optarg);
                break;
            case 'f':
                file_mode = 1;
                file_path = strdup(optarg);
                if (!file_path) {
                    perror("Memory allocation for file path");
                    return EXIT_FAILURE;
                }
                break;
            case 'L':
                length_range = optarg;
                if (sscanf(length_range, "%d-%d", &min_dlc, &max_dlc) != 2) {
                    printf("Error: Invalid length range format. Use min-max (e.g., 0-8)\n");
                    return EXIT_FAILURE;
                }
                if (min_dlc < 0 || min_dlc > 8 || max_dlc < 0 || max_dlc > 8 || min_dlc > max_dlc) {
                    printf("Error: Invalid length range. Min and max must be between 0-8, and min <= max\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'l':
                fixed_dlc = atoi(optarg);
                if (fixed_dlc < 0 || fixed_dlc > 8) {
                    printf("Error: Invalid fixed length. Must be between 0-8\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'T':
                global_timeout_seconds = atoi(optarg);
                if (global_timeout_seconds < 5 || global_timeout_seconds > 7200) {
                    printf("Error: Invalid initial frame timeout. Must be between 5-7200 seconds\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'F':
                frame_timeout_seconds = atoi(optarg);
                if (frame_timeout_seconds < 1 || frame_timeout_seconds > 60) {
                    printf("Error: Invalid select timeout. Must be between 1-60 seconds\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'M':
                max_loop_iterations = atoi(optarg);
                if (max_loop_iterations < 100000 || max_loop_iterations > 1000000000) {
                    printf("Error: Invalid max iterations. Must be between 100000-1000000000\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'E':
                max_consecutive_errors = atoi(optarg);
                if (max_consecutive_errors < 1 || max_consecutive_errors > 1000) {
                    printf("Error: Invalid max consecutive errors. Must be between 1-1000\n");
                    return EXIT_FAILURE;
                }
                break;
            case 'h':
                print_usage(argv[0]);
                return EXIT_SUCCESS;
            case '?':
                print_usage(argv[0]);
                return EXIT_FAILURE;
            default:
                printf("Unknown option: %c\n", opt);
                print_usage(argv[0]);
                return EXIT_FAILURE;
        }
    }
    srand(time(NULL));
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    socket_fd = init_can_socket(ifname);
    if (socket_fd < 0) {
        return EXIT_FAILURE;
    }

    /* 显示错误处理配置 */
    if (debug_mode) {
        printf("Error handling config: max_consecutive_errors=%d, timeouts=(%ds,%ds)\n",
               max_consecutive_errors, global_timeout_seconds, frame_timeout_seconds);
    }
    int result = 0;
    if (file_mode) {
        if (receive_mode) {
            result = receive_file(ifname, file_path);
            printf("File reception %s\n", result == 0 ? "completed successfully" : "failed");
        } else {
            result = transmit_file(ifname, interval_ns, file_path, fixed_can_id);
            printf("File transmission %s\n", result == 0 ? "completed successfully" : "failed");
        }
    } else {
        if (receive_mode) {
            if (max_frames <= 0) {
                printf("Error: In receive mode, you must specify the number of frames to receive using -n option\n");
                printf("Example: %s -r -n 1000\n", argv[0]);
                close(socket_fd);
                return EXIT_FAILURE;
            }
            receive_frames(ifname, max_frames);
        } else {
            if (max_frames <= 0) {
                printf("Error: In transmit mode, you must specify the number of frames to send using -n option\n");
                printf("Example: %s -n 1000\n", argv[0]);
                close(socket_fd);
                return EXIT_FAILURE;
            }
            transmit_frames(ifname, interval_ns, max_frames, fixed_can_id);
        }
    }
    if (socket_fd >= 0) {
        close(socket_fd);
    }
    free_frame_buffer();
    if (file_path) {
        free(file_path);
        file_path = NULL;
    }
    if (file_mode) {
        printf("Program completed with %s\n", result == 0 ? "success" : "errors");
    }
    return EXIT_SUCCESS;
}
